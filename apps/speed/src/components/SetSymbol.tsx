import * as React from 'react';
import { CoreAssets } from '../helpers/core_assets';
import { TextFormat } from '../helpers/fmt';
import { Rarity } from '../models/Rarity';

export enum SetSymbolSize {
  XS = 'xs',
  SM = 'sm',
  MD = 'md',
  XL = 'xl',
}

export interface ISetSymbolProps {
  setName: string;
  setCode: string;
  hoverText: boolean;
  collectorNumber?: string;
  rarity?: Rarity;
  size?: SetSymbolSize;
}

export function rarityToChar(rarity?: Rarity) {
  switch (rarity) {
    case undefined:
      return undefined;
    case Rarity.BASIC_LAND:
    case Rarity.COMMON:
      return 'C';
    case Rarity.UNCOMMON:
      return 'U';
    case Rarity.RARE:
      return 'R';
    case Rarity.MYTHIC_RARE:
      return 'M';
    case Rarity.SPECIAL:
      return 'S';
    default:
      // TODO: Add Bugsnag notifier when we work out how to get that working without causing <PERSON><PERSON> to lose it.
      console.error(`Invalid rarity ${rarity} converted to COMMON`);
      return undefined;
  }
}

export function SetSymbol(props: ISetSymbolProps) {
  const [hover, setHover] = React.useState(false);

  const size = props.size === undefined ? SetSymbolSize.XS : props.size;
  const char = rarityToChar(props.rarity);
  const rarityHoverMessage = props.rarity
    ? TextFormat.capitalize(props.rarity)
    : '';
  const collectorNumberText = props.collectorNumber
    ? ' - ' + props.collectorNumber
    : '';
  const imgSrc =
    char === undefined
      ? `${CoreAssets.iconHost()}/set_symbols/${props.setCode}_large.png`
      : `${CoreAssets.iconHost()}/set_symbols/${
          props.setCode
        }_${char}_large.png`;

  const handleMouseEnter = (evt: React.SyntheticEvent<HTMLElement>) => {
    evt.preventDefault();
    setHover(true);
  };

  const handleMouseLeave = (evt: React.SyntheticEvent<HTMLElement>) => {
    evt.preventDefault();
    setHover(false);
  };

  return (
    <div
      className={`flex set-symbol__${size}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <img src={imgSrc} alt={`${props.setName} set symbol`} />
      {props.hoverText && hover ? (
        <div className="set-symbol-tooltip-container">
          <div className="set-symbol-tooltip">
            <div>{props.setName}</div>
            <div>{rarityHoverMessage + collectorNumberText}</div>
          </div>
        </div>
      ) : null}
    </div>
  );
}
