export class TextFormat {
  static capitalize(str: string): string {
    if (!str || str.length == 0) {
      return '';
    } else {
      return str
        .split(' ')
        .map((word) => TextFormat.capitalizeWord(word))
        .filter((word) => word.length > 0)
        .join(' ');
    }
  }

  static capitalizeWord(word: string) {
    if (!word || word.length == 0) {
      return '';
    } else {
      return word[0].toUpperCase() + word.slice(1);
    }
  }

  static humanizeKey(key: string) {
    return key
      .split(RegExp('[-_]'))
      .map((value: string) => {
        return this.capitalizeWord(value);
      })
      .join(' ');
  }

  static pluralizedCards(count: number): string {
    return this.quantityOf('card', count, false);
  }

  static pluralizedCardsCapitalized(count: number): string {
    return this.quantityOf('card', count, false);
  }

  // Only appends 's' - nothing fancy
  static pluralize(word: string, count: number): string {
    return `${count === 1 ? word : word + 's'}`;
  }

  static quantityOf(word: string, count: number, humanizeZero = true): string {
    const prefix =
      humanizeZero && count <= 0 ? 'No' : NumberFormat.commaSeparated(count);
    return `${prefix} ${this.pluralize(word, count)}`;
  }
}

export class NumberFormat {
  static commaSeparated(num?: number): string {
    if (!num) {
      return '0';
    }
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
}
