

export function capitalizeWord(word: string): string {
  if (!word || word.length == 0) {
    return '';
  } else {
    return word[0].toUpperCase() + word.slice(1);
  }
}
export function capitalize(str: string): string {
  if (!str || str.length == 0) {
    return '';
  } else {
    return str
      .split(' ')
      .map((word) => capitalizeWord(word))
      .filter((word) => word.length > 0)
      .join(' ');
  }
}

export function humanizeKey(key: string): string {
  return key
    .split(RegExp('[-_]'))
    .map((value: string) => {
      return capitalizeWord(value);
    })
    .join(' ');
}

// Only appends 's' - nothing fancy
export function pluralize(word: string, count: number): string {
  return `${count === 1 ? word : word + 's'}`;
}

export function commaSeparated(num?: number): string {
  if (!num) {
    return '0';
  }
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

export function quantityOf(
  word: string,
  count: number,
  humanizeZero = true,
): string {
  const prefix = humanizeZero && count <= 0 ? 'No' : commaSeparated(count);
  return `${prefix} ${pluralize(word, count)}`;
}

export function pluralizedCards(count: number): string {
  return quantityOf('card', count, false);
}

export function pluralizedCardsCapitalized(count: number): string {
  return quantityOf('card', count, false);
}
