{"name": "@cardcastle/speed", "private": true, "sideEffects": false, "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "lint": "eslint ./", "start": "vite start", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@tanstack/react-router": "^1.123.2", "@tanstack/react-router-devtools": "^1.123.2", "@tanstack/react-router-with-query": "^1.123.2", "@tanstack/react-start": "^1.123.2", "fuse.js": "^7.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-select": "^5.10.1", "redaxios": "^0.5.1", "tailwind-merge": "^3.3.1", "vite": "^6.3.5"}, "devDependencies": {"@types/node": "^24.0.8", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-basic-ssl": "^2.0.0", "autoprefixer": "^10.4.21", "jsdom": "^26.1.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "vite-plugin-checker": "^0.9.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}}